"use client"

import type React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { LoadingSpinner } from "@/components/ui/loading"
import { ScrollToTop } from "@/components/scroll-to-top"
import { trackEvent } from "@/components/analytics"
import { validateForm, contactFormRules } from "@/lib/form-validation"
import { useAnnounceToScreenReader } from "@/hooks/use-focus-management"
import { sendContactEmail } from "@/lib/email-service"
import {
  Shield,
  DollarSign,
  Settings,
  Phone,
  Mail,
  CheckCircle,
  TrendingUp,
  Lock,
  Zap,
  ArrowRight,
  Star,
  Menu,
  X,
  Check,
  AlertCircle,
} from "lucide-react"
import { useState, useEffect } from "react"
import Link from "next/link"

export default function HomePage() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isFormSubmitting, setIsFormSubmitting] = useState(false)
  const [formSubmitted, setFormSubmitted] = useState(false)
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  })
  const [scrollProgress, setScrollProgress] = useState(0)
  const [submitError, setSubmitError] = useState<string | null>(null)

  const announce = useAnnounceToScreenReader()

  useEffect(() => {
    const handleScroll = () => {
      const totalHeight = document.documentElement.scrollHeight - window.innerHeight
      const progress = (window.scrollY / totalHeight) * 100
      setScrollProgress(progress)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const scrollToContact = () => {
    document.getElementById("contact")?.scrollIntoView({ behavior: "smooth" })
    setIsMobileMenuOpen(false)
  }

  const scrollToSection = (sectionId: string) => {
    document.getElementById(sectionId)?.scrollIntoView({ behavior: "smooth" })
    setIsMobileMenuOpen(false)
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: '' }))
    }
  }

  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    setIsFormSubmitting(true)
    setSubmitError(null)

    // Validate form
    const errors = validateForm(formData, contactFormRules)
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors)
      setIsFormSubmitting(false)
      announce('Please fix the form errors and try again', 'assertive')
      return
    }

    try {
      // Track form submission attempt
      trackEvent('form_submit_attempt', 'contact', 'contact_form')

      // Try to send email using our email service
      const emailResult = await sendContactEmail(formData)

      if (emailResult.success) {
        // Success
        setFormSubmitted(true)
        setFormData({ name: '', email: '', company: '', message: '' })

        const successMessage = emailResult.method === 'mailto'
          ? 'Your email client has been opened with the message. Please send it to complete your request.'
          : 'Message sent successfully! We will contact you within 24 hours.'

        announce(successMessage, 'polite')

        // Track successful submission
        trackEvent('form_submit_success', 'contact', 'contact_form', { method: emailResult.method })

        // Reset success state after 8 seconds (longer for mailto)
        setTimeout(() => setFormSubmitted(false), 8000)
      } else {
        throw new Error('Failed to send email')
      }

    } catch (error) {
      console.error('Form submission error:', error)
      setSubmitError('Failed to send message. Please try again or contact us directly.')
      announce('Failed to send message. Please try again.', 'assertive')

      // Track failed submission
      trackEvent('form_submit_error', 'contact', 'contact_form')
    } finally {
      setIsFormSubmitting(false)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <div
        className="fixed top-0 left-0 h-1 bg-gradient-to-r from-accent to-primary z-50 transition-all duration-300"
        style={{ width: `${scrollProgress}%` }}
      />

      <header className="sticky top-0 z-50 glass-effect border-b border-border/50">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Shield className="h-10 w-10 text-accent" />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-accent rounded-full animate-pulse" />
              </div>
              <div>
                <span className="text-2xl font-serif font-black text-foreground">EZI Data</span>
                <div className="text-xs text-muted-foreground font-medium tracking-wider">CONSULTANCY</div>
              </div>
            </div>

            <button
              className="md:hidden p-2 text-foreground hover:text-accent transition-colors"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>

            <nav className="hidden md:flex items-center space-x-8">
              <button
                onClick={() => scrollToSection("services")}
                className="text-muted-foreground hover:text-accent transition-all duration-300 font-medium"
              >
                Services
              </button>
              <button
                onClick={() => scrollToSection("process")}
                className="text-muted-foreground hover:text-accent transition-all duration-300 font-medium"
              >
                Process
              </button>
              <button
                onClick={() => scrollToSection("why-us")}
                className="text-muted-foreground hover:text-accent transition-all duration-300 font-medium"
              >
                Why Us
              </button>
              <Button
                onClick={scrollToContact}
                className="bg-accent hover:bg-accent/90 text-accent-foreground font-semibold px-6 py-2 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                Get Started
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </nav>
          </div>

          {isMobileMenuOpen && (
            <div className="md:hidden mt-4 pb-4 border-t border-border/50">
              <nav className="flex flex-col space-y-4 pt-4">
                <button
                  onClick={() => scrollToSection("services")}
                  className="text-left text-muted-foreground hover:text-accent transition-all duration-300 font-medium"
                >
                  Services
                </button>
                <button
                  onClick={() => scrollToSection("process")}
                  className="text-left text-muted-foreground hover:text-accent transition-all duration-300 font-medium"
                >
                  Process
                </button>
                <button
                  onClick={() => scrollToSection("why-us")}
                  className="text-left text-muted-foreground hover:text-accent transition-all duration-300 font-medium"
                >
                  Why Us
                </button>
                <Button
                  onClick={scrollToContact}
                  className="bg-accent hover:bg-accent/90 text-accent-foreground font-semibold shadow-lg hover:shadow-xl transition-all duration-300 w-full justify-center"
                >
                  Get Started
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </nav>
            </div>
          )}
        </div>
      </header>

      <section className="relative py-32 px-6 hero-bg overflow-hidden min-h-[90vh] flex items-center">
        <div className="absolute inset-0 network-bg opacity-30" />
        <div className="absolute top-20 right-20 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-20 left-20 w-80 h-80 bg-emerald-500/10 rounded-full blur-3xl animate-pulse delay-1000" />

        <div className="absolute top-10 right-10 opacity-20">
          <img src="/placeholder-o939c.png" alt="Cybersecurity Network" className="w-96 h-96 object-contain" />
        </div>
        <div className="absolute bottom-10 left-10 opacity-15">
          <img src="/cyan-data-dashboard.png" alt="Data Analytics" className="w-72 h-72 object-contain" />
        </div>

        <div className="container mx-auto text-center max-w-5xl relative z-10">
          <div className="inline-flex items-center px-4 py-2 bg-accent/20 border border-accent/30 rounded-full text-accent font-medium text-sm mb-8 backdrop-blur-sm">
            <Star className="w-4 h-4 mr-2" />
            Trusted by Enterprise Telecom Leaders
          </div>
          <h1 className="text-5xl md:text-7xl font-serif font-black text-foreground mb-8 leading-tight">
            We fix your data. <span className="gradient-text">Stop fraud.</span>{" "}
            <span className="gradient-text">Secure revenue.</span> That's it.
          </h1>
          <p className="text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto font-medium">
            <strong className="text-foreground">Fraud & Revenue Solutions – No Fluff, Just Results</strong>
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              onClick={scrollToContact}
              size="lg"
              className="bg-accent hover:bg-accent/90 text-accent-foreground text-lg px-10 py-4 font-semibold shadow-2xl hover:shadow-accent/25 transition-all duration-300"
            >
              Get Started Now
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="text-lg px-10 py-4 font-semibold border-2 hover:bg-accent/5 transition-all duration-300 bg-transparent border-accent/30"
            >
              View Case Studies
            </Button>
          </div>
        </div>
      </section>

      <section id="services" className="py-24 px-6 security-pattern relative">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-muted/20 to-transparent" />

        <div className="absolute top-20 left-10 opacity-10">
          <img src="/placeholder-it3qq.png" alt="Telecom Infrastructure" className="w-48 h-48 object-contain" />
        </div>
        <div className="absolute bottom-20 right-10 opacity-10">
          <img src="/cyan-cybersecurity-shield.png" alt="Security Shield" className="w-60 h-60 object-contain" />
        </div>

        <div className="container mx-auto max-w-7xl relative z-10">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-serif font-black text-foreground mb-6">What We Do</h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Three core services. Maximum impact. Zero complexity.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border-2 border-border hover:border-accent/50 bg-card/80 backdrop-blur-sm transition-all duration-300 hover:shadow-2xl group relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
                <img
                  src="/fraud-detection-visualization.png"
                  alt="Fraud Detection"
                  className="w-full h-full object-contain"
                />
              </div>
              <CardContent className="p-10 relative z-10">
                <div className="flex items-center mb-6">
                  <div className="p-3 bg-accent/10 rounded-xl mr-4 group-hover:bg-accent/20 transition-colors">
                    <Shield className="h-10 w-10 text-accent" />
                  </div>
                  <h3 className="text-2xl font-serif font-bold text-card-foreground">Fraud Management</h3>
                </div>
                <div className="space-y-4 text-muted-foreground text-lg">
                  <p className="font-medium">We analyze your data. Find fraud. Stop it.</p>
                  <p>No fancy hardware—just smarter rules, better detection.</p>
                  <p>Works with your existing systems.</p>
                </div>
                <div className="mt-8 flex items-center text-accent font-semibold">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Up to 40% fraud reduction
                </div>
              </CardContent>
            </Card>

            <Card className="border-2 border-border hover:border-accent/50 bg-card/80 backdrop-blur-sm transition-all duration-300 hover:shadow-2xl group relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
                <img
                  src="/revenue-analytics-chart.png"
                  alt="Revenue Analytics"
                  className="w-full h-full object-contain"
                />
              </div>
              <CardContent className="p-10 relative z-10">
                <div className="flex items-center mb-6">
                  <div className="p-3 bg-accent/10 rounded-xl mr-4 group-hover:bg-accent/20 transition-colors">
                    <DollarSign className="h-10 w-10 text-accent" />
                  </div>
                  <h3 className="text-2xl font-serif font-bold text-card-foreground">Revenue Assurance</h3>
                </div>
                <div className="space-y-4 text-muted-foreground text-lg">
                  <p className="font-medium">We find leaks. You keep more money.</p>
                  <p>No "big consulting talk"—just clear fixes for billing errors.</p>
                  <p>Faster reconciliation = fewer losses.</p>
                </div>
                <div className="mt-8 flex items-center text-accent font-semibold">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Average 15% revenue recovery
                </div>
              </CardContent>
            </Card>

            <Card className="border-2 border-border hover:border-accent/50 bg-card/80 backdrop-blur-sm transition-all duration-300 hover:shadow-2xl group relative overflow-hidden">
              <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
                <img
                  src="/system-optimization-dashboard.png"
                  alt="System Optimization"
                  className="w-full h-full object-contain"
                />
              </div>
              <CardContent className="p-10 relative z-10">
                <div className="flex items-center mb-6">
                  <div className="p-3 bg-accent/10 rounded-xl mr-4 group-hover:bg-accent/20 transition-colors">
                    <Settings className="h-10 w-10 text-accent" />
                  </div>
                  <h3 className="text-2xl font-serif font-bold text-card-foreground">BSS/OSS Tuning</h3>
                </div>
                <div className="space-y-4 text-muted-foreground text-lg">
                  <p className="font-medium">Slow system? Bad data? We clean it up.</p>
                  <p>No rip-and-replace—just optimize what you have.</p>
                  <p>Faster. Accurate. Done.</p>
                </div>
                <div className="mt-8 flex items-center text-accent font-semibold">
                  <Zap className="w-5 h-5 mr-2" />
                  60% faster processing
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <section id="process" className="py-24 px-6 data-viz-bg relative">
        <div className="absolute inset-0 bg-gradient-to-r from-background/90 via-background/95 to-background/90" />

        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-5">
          <img src="/data-flow-diagram.png" alt="Data Process Flow" className="w-[800px] h-[600px] object-contain" />
        </div>

        <div className="container mx-auto max-w-5xl relative z-10">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-serif font-black text-foreground mb-6">How We Work</h2>
            <p className="text-xl text-muted-foreground">Simple. Transparent. Effective.</p>
          </div>
          <div className="space-y-12">
            {[
              { step: 1, text: "You give us access to your data (billing, CDRs, logs).", icon: Lock },
              { step: 2, text: "We find the problems (fraud, leaks, inefficiencies).", icon: Shield },
              { step: 3, text: "You get a fix (simple reports, actionable steps).", icon: TrendingUp },
              { step: 4, text: "You save money.", icon: DollarSign },
            ].map((item, index) => (
              <div key={index} className="flex items-center space-x-8 group">
                <div className="bg-accent text-accent-foreground rounded-2xl w-16 h-16 flex items-center justify-center font-serif font-black text-2xl flex-shrink-0 shadow-lg group-hover:shadow-xl transition-all duration-300">
                  {item.step}
                </div>
                <div className="flex items-center space-x-4 flex-1">
                  <item.icon className="w-8 h-8 text-accent flex-shrink-0" />
                  <p className="text-xl text-muted-foreground font-medium">{item.text}</p>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-16 text-center">
            <Card className="bg-accent/5 border-accent/20 border-2">
              <CardContent className="p-8">
                <p className="text-xl text-foreground font-semibold">
                  No extra costs. No unnecessary tools. Just the work.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <section id="why-us" className="py-24 px-6 security-pattern relative">
        <div className="absolute inset-0 bg-gradient-to-b from-muted/30 via-muted/20 to-muted/30" />

        <div className="absolute top-10 right-20 opacity-8">
          <img src="/trust-badge-blue.png" alt="Trust Badge" className="w-72 h-72 object-contain" />
        </div>

        <div className="container mx-auto max-w-5xl relative z-10">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-serif font-black text-foreground mb-6">Why Us</h2>
            <p className="text-xl text-muted-foreground">No nonsense. No fluff. Just results that matter.</p>
          </div>
          <div className="space-y-8">
            {[
              { title: "No sales pitches", desc: "We only speak in results." },
              { title: "No bloat", desc: "We don't sell hardware or software." },
              { title: "No long contracts", desc: "Pay for what you need." },
            ].map((item, index) => (
              <Card
                key={index}
                className="border-2 border-border hover:border-accent/50 transition-all duration-300 hover:shadow-lg"
              >
                <CardContent className="p-8">
                  <div className="flex items-center space-x-6">
                    <CheckCircle className="h-8 w-8 text-accent flex-shrink-0" />
                    <div>
                      <p className="font-serif font-bold text-foreground text-lg">{item.title}</p>
                      <p className="text-lg text-muted-foreground">{item.desc}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="mt-16 text-center">
            <blockquote className="text-4xl font-serif font-black text-foreground italic gradient-text">
              "We don't talk. We do."
            </blockquote>
          </div>
        </div>
      </section>

      <section id="contact" className="py-24 px-6 hero-bg relative">
        <div className="absolute inset-0 network-bg opacity-20" />

        <div className="absolute bottom-10 right-10 opacity-10">
          <img src="/placeholder-qtdkx.png" alt="Communication" className="w-60 h-60 object-contain" />
        </div>

        <div className="container mx-auto max-w-6xl relative z-10">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-serif font-black text-foreground mb-6">Get Started</h2>
            <p className="text-2xl text-muted-foreground font-medium">
              Contact us. Send your data. See the difference.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-16">
            {/* Contact Info */}
            <div className="space-y-10">
              <Card className="border-2 border-accent/20 bg-accent/5">
                <CardContent className="p-8">
                  <div className="flex items-center space-x-4 mb-4">
                    <Mail className="h-8 w-8 text-accent" />
                    <div>
                      <p className="font-serif font-bold text-foreground text-lg">Email</p>
                      <a
                        href="mailto:<EMAIL>"
                        className="text-muted-foreground text-lg hover:text-accent transition-colors"
                      >
                        <EMAIL>
                      </a>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-2 border-accent/20 bg-accent/5">
                <CardContent className="p-8">
                  <div className="flex items-center space-x-4 mb-4">
                    <Phone className="h-8 w-8 text-accent" />
                    <div>
                      <p className="font-serif font-bold text-foreground text-lg">Phone</p>
                      <a
                        href="tel:+15551234567"
                        className="text-muted-foreground text-lg hover:text-accent transition-colors"
                      >
                        +****************
                      </a>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="mt-12">
                <blockquote className="text-2xl font-serif font-bold text-foreground italic gradient-text">
                  "Stop losing money. Let's fix it."
                </blockquote>
              </div>
            </div>

            <Card className="border-2 border-border bg-card shadow-2xl">
              <CardContent className="p-10">
                {formSubmitted ? (
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-accent/20 rounded-full flex items-center justify-center mx-auto mb-6">
                      <Check className="w-8 h-8 text-accent" />
                    </div>
                    <h3 className="text-2xl font-serif font-bold text-foreground mb-4">Message Sent!</h3>
                    <p className="text-muted-foreground text-lg">
                      Thank you for reaching out. We'll contact you within 24 hours.
                    </p>
                  </div>
                ) : (
                  <form className="space-y-6" onSubmit={handleFormSubmit}>
                    <div>
                      <label htmlFor="name" className="block text-sm font-semibold text-card-foreground mb-3">
                        Name *
                      </label>
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        disabled={isFormSubmitting}
                        className={`bg-input border-2 focus:border-accent h-12 text-lg disabled:opacity-50 ${
                          formErrors.name ? 'border-destructive' : 'border-border'
                        }`}
                        aria-describedby={formErrors.name ? "name-error" : undefined}
                        aria-invalid={!!formErrors.name}
                      />
                      {formErrors.name && (
                        <p id="name-error" className="mt-2 text-sm text-destructive flex items-center">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.name}
                        </p>
                      )}
                    </div>
                    <div>
                      <label htmlFor="email" className="block text-sm font-semibold text-card-foreground mb-3">
                        Email *
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        disabled={isFormSubmitting}
                        className={`bg-input border-2 focus:border-accent h-12 text-lg disabled:opacity-50 ${
                          formErrors.email ? 'border-destructive' : 'border-border'
                        }`}
                        aria-describedby={formErrors.email ? "email-error" : undefined}
                        aria-invalid={!!formErrors.email}
                      />
                      {formErrors.email && (
                        <p id="email-error" className="mt-2 text-sm text-destructive flex items-center">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.email}
                        </p>
                      )}
                    </div>
                    <div>
                      <label htmlFor="company" className="block text-sm font-semibold text-card-foreground mb-3">
                        Company
                      </label>
                      <Input
                        id="company"
                        name="company"
                        type="text"
                        value={formData.company}
                        onChange={handleInputChange}
                        disabled={isFormSubmitting}
                        className={`bg-input border-2 focus:border-accent h-12 text-lg disabled:opacity-50 ${
                          formErrors.company ? 'border-destructive' : 'border-border'
                        }`}
                        aria-describedby={formErrors.company ? "company-error" : undefined}
                        aria-invalid={!!formErrors.company}
                      />
                      {formErrors.company && (
                        <p id="company-error" className="mt-2 text-sm text-destructive flex items-center">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.company}
                        </p>
                      )}
                    </div>
                    <div>
                      <label htmlFor="message" className="block text-sm font-semibold text-card-foreground mb-3">
                        Message *
                      </label>
                      <Textarea
                        id="message"
                        name="message"
                        rows={5}
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                        disabled={isFormSubmitting}
                        className={`bg-input border-2 focus:border-accent text-lg resize-none disabled:opacity-50 ${
                          formErrors.message ? 'border-destructive' : 'border-border'
                        }`}
                        placeholder="Tell us about your fraud management or revenue assurance challenges..."
                        aria-describedby={formErrors.message ? "message-error" : undefined}
                        aria-invalid={!!formErrors.message}
                      />
                      {formErrors.message && (
                        <p id="message-error" className="mt-2 text-sm text-destructive flex items-center">
                          <AlertCircle className="w-4 h-4 mr-1" />
                          {formErrors.message}
                        </p>
                      )}
                    </div>
                    {submitError && (
                      <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
                        <p className="text-destructive text-sm flex items-center">
                          <AlertCircle className="w-4 h-4 mr-2" />
                          {submitError}
                        </p>
                      </div>
                    )}

                    <Button
                      type="submit"
                      disabled={isFormSubmitting}
                      className="w-full bg-accent hover:bg-accent/90 text-accent-foreground font-semibold text-lg py-4 shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isFormSubmitting ? (
                        <>
                          <LoadingSpinner size="sm" className="mr-2" />
                          Sending...
                        </>
                      ) : (
                        <>
                          Send Message
                          <ArrowRight className="ml-2 h-5 w-5" />
                        </>
                      )}
                    </Button>
                  </form>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <footer className="bg-primary py-16 px-6 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-accent via-emerald-500 to-accent" />

        <div className="container mx-auto max-w-7xl relative z-10">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-4 mb-8 md:mb-0">
              <div className="relative">
                <Shield className="h-10 w-10 text-accent" />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-accent rounded-full" />
              </div>
              <div>
                <span className="text-2xl font-serif font-black text-primary-foreground">EZI Data</span>
                <div className="text-xs text-primary-foreground/70 font-medium tracking-wider">CONSULTANCY</div>
              </div>
            </div>
            <div className="text-center md:text-right">
              <p className="text-primary-foreground font-medium text-lg">
                © 2024 EZI Data Consultancy. All rights reserved.
              </p>
              <p className="text-primary-foreground/70 mt-2 font-medium">
                Fraud & Revenue Solutions – No Fluff, Just Results
              </p>
              <div className="flex justify-center md:justify-end space-x-4 mt-4">
                <Link href="/privacy-policy" className="text-primary-foreground/70 hover:text-primary-foreground text-sm transition-colors">
                  Privacy Policy
                </Link>
                <Link href="/terms-of-service" className="text-primary-foreground/70 hover:text-primary-foreground text-sm transition-colors">
                  Terms of Service
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>

      <ScrollToTop />
    </div>
  )
}
