@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Updated color tokens for premium enterprise cybersecurity aesthetic */
  --background: #0f172a; /* Dark slate background instead of white */
  --foreground: #f8fafc; /* Light text for contrast */
  --card: #1e293b; /* Dark slate cards */
  --card-foreground: #f8fafc; /* Light card text */
  --popover: #1e293b; /* Dark popover */
  --popover-foreground: #f8fafc; /* Light popover text */
  --primary: #0891b2; /* Brighter cyan for visibility */
  --primary-foreground: #ffffff;
  --secondary: #334155; /* Medium slate */
  --secondary-foreground: #f8fafc; /* Light text */
  --muted: #334155; /* Medium slate for muted sections */
  --muted-foreground: #94a3b8; /* Medium slate for muted text */
  --accent: #10b981; /* Vibrant emerald for key highlights */
  --accent-foreground: #ffffff;
  --destructive: #ef4444; /* Brighter red */
  --destructive-foreground: #ffffff;
  --border: #334155; /* Medium slate border */
  --input: #1e293b; /* Dark input background */
  --ring: #10b981;
  --chart-1: #10b981;
  --chart-2: #0891b2; /* Brighter cyan */
  --chart-3: #94a3b8; /* Medium slate */
  --chart-4: #ef4444; /* Brighter red */
  --chart-5: #334155; /* Medium slate */
  --radius: 0.5rem;
  --sidebar: #1e293b; /* Dark sidebar */
  --sidebar-foreground: #f8fafc; /* Light sidebar text */
  --sidebar-primary: #0891b2; /* Brighter cyan */
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #334155; /* Medium slate */
  --sidebar-accent-foreground: #f8fafc; /* Light text */
  --sidebar-border: #334155; /* Medium slate */
  --sidebar-ring: #10b981;
  --font-inter: "Inter", sans-serif;
  --font-montserrat: "Montserrat", serif;
}

.dark {
  /* Premium dark mode with sophisticated cybersecurity feel */
  --background: #0f172a;
  --foreground: #f8fafc;
  --card: #1e293b;
  --card-foreground: #f8fafc;
  --popover: #1e293b;
  --popover-foreground: #f8fafc;
  --primary: #0891b2;
  --primary-foreground: #ffffff;
  --secondary: #334155;
  --secondary-foreground: #f8fafc;
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --accent: #10b981;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #334155;
  --input: #1e293b;
  --ring: #10b981;
  --chart-1: #10b981;
  --chart-2: #0891b2;
  --chart-3: #94a3b8;
  --chart-4: #ef4444;
  --chart-5: #334155;
  --sidebar: #1e293b;
  --sidebar-foreground: #f8fafc;
  --sidebar-primary: #0891b2;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #334155;
  --sidebar-accent-foreground: #f8fafc;
  --sidebar-border: #334155;
  --sidebar-ring: #10b981;
  --font-inter: "Inter", sans-serif;
  --font-montserrat: "Montserrat", serif;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --font-sans: var(--font-inter);
  --font-serif: var(--font-montserrat);
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Enhanced premium visual effects and backgrounds */
  .hero-bg {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
    position: relative;
  }

  .hero-bg::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(16, 185, 129, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(8, 145, 178, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.1) 0%, transparent 50%);
    pointer-events: none;
  }

  .network-bg {
    background-image: radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.1) 0%, transparent 25%),
      radial-gradient(circle at 75% 75%, rgba(8, 145, 178, 0.1) 0%, transparent 25%),
      linear-gradient(45deg, transparent 49%, rgba(16, 185, 129, 0.03) 50%, transparent 51%),
      linear-gradient(-45deg, transparent 49%, rgba(8, 145, 178, 0.03) 50%, transparent 51%);
    background-size: 100px 100px, 150px 150px, 20px 20px, 20px 20px;
    animation: networkPulse 20s ease-in-out infinite;
  }

  .security-pattern {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    position: relative;
  }

  .security-pattern::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(16, 185, 129, 0.05) 2px,
        rgba(16, 185, 129, 0.05) 4px
      ),
      repeating-linear-gradient(
        -45deg,
        transparent,
        transparent 2px,
        rgba(8, 145, 178, 0.05) 2px,
        rgba(8, 145, 178, 0.05) 4px
      );
    pointer-events: none;
  }

  .data-viz-bg {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 50%, #1e293b 100%);
    position: relative;
  }

  .data-viz-bg::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(circle at 30% 70%, rgba(16, 185, 129, 0.08) 0%, transparent 40%),
      radial-gradient(circle at 70% 30%, rgba(8, 145, 178, 0.08) 0%, transparent 40%),
      linear-gradient(90deg, transparent 49%, rgba(16, 185, 129, 0.02) 50%, transparent 51%);
    background-size: 200px 200px, 250px 250px, 50px 50px;
    animation: dataFlow 15s linear infinite;
    pointer-events: none;
  }

  .tech-pattern {
    background-image: radial-gradient(circle at 25% 25%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(22, 78, 99, 0.1) 0%, transparent 50%);
  }

  .gradient-text {
    background: linear-gradient(135deg, #10b981 0%, #0891b2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .glass-effect {
    backdrop-filter: blur(16px);
    background: rgba(15, 23, 42, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Added animations for dynamic effects */
  @keyframes networkPulse {
    0%,
    100% {
      opacity: 0.5;
    }
    50% {
      opacity: 0.8;
    }
  }

  @keyframes dataFlow {
    0% {
      background-position: 0% 0%, 0% 0%, 0% 0%;
    }
    100% {
      background-position: 100% 100%, -100% -100%, 50% 50%;
    }
  }

  /* Enhanced card hover effects */
  .hover-glow:hover {
    box-shadow: 0 0 30px rgba(16, 185, 129, 0.3);
    transform: translateY(-2px);
    transition: all 0.3s ease;
  }

  /* Subtle scroll animations */
  @media (prefers-reduced-motion: no-preference) {
    .animate-on-scroll {
      opacity: 0;
      transform: translateY(20px);
      transition: opacity 0.6s ease, transform 0.6s ease;
    }

    .animate-on-scroll.in-view {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
