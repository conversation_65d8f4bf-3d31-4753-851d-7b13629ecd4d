"use client"

import { useEffect } from "react"

declare global {
  interface Window {
    gtag: (...args: any[]) => void
  }
}

export function Analytics() {
  useEffect(() => {
    // Analytics will only load if manually configured later
    console.log("Analytics component loaded - ready for future GA configuration")
  }, [])

  return null
}

// Track custom events - safe to call even without GA configured
export const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("event", eventName, parameters)
  } else {
    console.log("Event tracked:", eventName, parameters)
  }
}
