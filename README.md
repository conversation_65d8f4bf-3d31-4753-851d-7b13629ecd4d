# EZI Data Consultancy Website

A premium, enterprise-grade website for EZI Data Consultancy - specialists in telecom fraud management, revenue assurance, and BSS/OSS optimization.

## 🚀 Features

- **Premium Design**: Enterprise-grade cybersecurity aesthetic with sophisticated dark theme
- **Fully Responsive**: Optimized for desktop, tablet, and mobile devices
- **SEO Optimized**: Complete meta tags, sitemap, and robots.txt for search engine visibility
- **Performance Focused**: Fast loading with optimized images and animations
- **Contact Form**: Functional contact form with client-side validation
- **Accessibility**: WCAG compliant with proper ARIA labels and semantic HTML
- **Modern Tech Stack**: Built with Next.js 14, TypeScript, and Tailwind CSS

## 🎨 Design Highlights

- **Color Palette**: Professional cybersecurity theme with cyan and emerald accents
- **Typography**: Montserrat for headings, Inter for body text
- **Visual Effects**: Subtle animations, gradient overlays, and tech-inspired patterns
- **Imagery**: Custom cybersecurity and data visualization graphics
- **Layout**: Clean, modern sections with optimal spacing and contrast

## 📁 Project Structure

\`\`\`
├── app/
│   ├── globals.css          # Global styles and design system
│   ├── layout.tsx           # Root layout with SEO metadata
│   ├── page.tsx             # Main homepage
│   ├── loading.tsx          # Loading page component
│   └── not-found.tsx        # Custom 404 page
├── public/
│   ├── favicon.ico          # Site favicon
│   ├── robots.txt           # Search engine directives
│   ├── sitemap.xml          # Site structure for SEO
│   └── *.png                # Cybersecurity themed images
├── README.md                # This file
└── DEPLOYMENT.md            # Deployment instructions
\`\`\`

## 🛠️ Technology Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Fonts**: Montserrat, Inter (Google Fonts)
- **Icons**: Lucide React
- **Deployment**: Optimized for Vercel

## 🎯 Key Sections

1. **Hero Section**: Compelling headline with cybersecurity visuals
2. **Services**: Three core offerings with detailed descriptions
3. **About**: Company expertise and approach
4. **Why Choose Us**: Key differentiators and benefits
5. **Contact**: Professional contact form with company details

## 📧 Contact Form

The contact form includes:
- Client-side validation
- Professional styling
- Success/error messaging
- Responsive design
- Accessibility features

## 🔧 Customization

### Colors
Edit the CSS custom properties in `app/globals.css`:
\`\`\`css
:root {
  --primary: 158 100% 50%;    /* Cyan */
  --secondary: 160 84% 39%;   /* Emerald */
  --accent: 217 91% 60%;      /* Blue */
}
\`\`\`

### Content
Update content in `app/page.tsx` by modifying the text within each section component.

### Images
Replace images in the `public/` directory with your own branded visuals.

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

## ♿ Accessibility

- WCAG 2.1 AA compliant
- Semantic HTML structure
- Proper ARIA labels
- Keyboard navigation support
- Screen reader optimized

## 🚀 Getting Started

1. Clone or download the project
2. Install dependencies: `npm install`
3. Run development server: `npm run dev`
4. Open [http://localhost:3000](http://localhost:3000)

## 📈 SEO Features

- Comprehensive meta tags
- Open Graph and Twitter Card support
- Structured data markup
- Optimized images with alt text
- Clean URL structure
- XML sitemap
- Robots.txt configuration

## 🔒 Security

- No external dependencies for core functionality
- Client-side form validation
- Secure headers configuration
- HTTPS ready

## 📞 Support

For technical support or customization requests, contact the development team or refer to the deployment documentation.

---

**EZI Data Consultancy** - Protecting telecom revenue through advanced fraud detection and system optimization.
