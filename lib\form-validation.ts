export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: string) => string | null
}

export interface ValidationRules {
  [key: string]: ValidationRule
}

export interface ValidationErrors {
  [key: string]: string
}

export function validateField(value: string, rules: ValidationRule): string | null {
  // Required validation
  if (rules.required && (!value || value.trim().length === 0)) {
    return 'This field is required'
  }

  // Skip other validations if field is empty and not required
  if (!value || value.trim().length === 0) {
    return null
  }

  // Min length validation
  if (rules.minLength && value.length < rules.minLength) {
    return `Must be at least ${rules.minLength} characters long`
  }

  // Max length validation
  if (rules.maxLength && value.length > rules.maxLength) {
    return `Must be no more than ${rules.maxLength} characters long`
  }

  // Pattern validation
  if (rules.pattern && !rules.pattern.test(value)) {
    return 'Invalid format'
  }

  // Custom validation
  if (rules.custom) {
    return rules.custom(value)
  }

  return null
}

export function validateForm(data: Record<string, string>, rules: ValidationRules): ValidationErrors {
  const errors: ValidationErrors = {}

  Object.keys(rules).forEach(field => {
    const value = data[field] || ''
    const error = validateField(value, rules[field])
    if (error) {
      errors[field] = error
    }
  })

  return errors
}

// Common validation rules
export const emailRule: ValidationRule = {
  required: true,
  pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  custom: (value: string) => {
    if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return 'Please enter a valid email address'
    }
    return null
  }
}

export const nameRule: ValidationRule = {
  required: true,
  minLength: 2,
  maxLength: 100,
  pattern: /^[a-zA-Z\s'-]+$/,
  custom: (value: string) => {
    if (value && !/^[a-zA-Z\s'-]+$/.test(value)) {
      return 'Name can only contain letters, spaces, hyphens, and apostrophes'
    }
    return null
  }
}

export const companyRule: ValidationRule = {
  required: false,
  maxLength: 100,
  pattern: /^[a-zA-Z0-9\s&.,'-]+$/,
  custom: (value: string) => {
    if (value && !/^[a-zA-Z0-9\s&.,'-]+$/.test(value)) {
      return 'Company name contains invalid characters'
    }
    return null
  }
}

export const messageRule: ValidationRule = {
  required: true,
  minLength: 10,
  maxLength: 1000,
  custom: (value: string) => {
    if (value && value.trim().length < 10) {
      return 'Message must be at least 10 characters long'
    }
    return null
  }
}

// Contact form validation rules
export const contactFormRules: ValidationRules = {
  name: nameRule,
  email: emailRule,
  company: companyRule,
  message: messageRule
}
