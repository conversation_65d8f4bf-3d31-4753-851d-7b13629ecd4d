import type { Metadata } from 'next'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Terms of Service - Linda Meals',
  description: 'Terms of service for Linda Meals services.',
}

export default function TermsOfServicePage() {
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-6 py-12 max-w-4xl">
        <div className="mb-8">
          <Link href="/">
            <Button variant="outline" className="mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Home
            </Button>
          </Link>
          <h1 className="text-4xl font-serif font-black text-foreground mb-4">Terms of Service</h1>
          <p className="text-muted-foreground">Last updated: {new Date().toLocaleDateString()}</p>
        </div>

        <Card>
          <CardContent className="p-8 space-y-8">
            <section>
              <h2 className="text-2xl font-serif font-bold text-foreground mb-4">1. Acceptance of Terms</h2>
              <p className="text-muted-foreground">
                By accessing and using EZI Data Consultancy services, you accept and agree to be bound by the terms 
                and provision of this agreement.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-serif font-bold text-foreground mb-4">2. Services</h2>
              <p className="text-muted-foreground mb-4">
                EZI Data Consultancy provides:
              </p>
              <ul className="list-disc list-inside text-muted-foreground space-y-2">
                <li>Fraud Management and Detection Services</li>
                <li>Revenue Assurance Solutions</li>
                <li>BSS/OSS System Optimization</li>
                <li>Telecom Data Analysis and Consulting</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-serif font-bold text-foreground mb-4">3. Client Responsibilities</h2>
              <p className="text-muted-foreground mb-4">
                Clients are responsible for:
              </p>
              <ul className="list-disc list-inside text-muted-foreground space-y-2">
                <li>Providing accurate and complete information</li>
                <li>Ensuring data access permissions are properly authorized</li>
                <li>Maintaining confidentiality of sensitive information</li>
                <li>Timely payment of agreed fees</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-serif font-bold text-foreground mb-4">4. Confidentiality</h2>
              <p className="text-muted-foreground">
                We maintain strict confidentiality of all client data and information. All projects are covered 
                by comprehensive non-disclosure agreements.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-serif font-bold text-foreground mb-4">5. Limitation of Liability</h2>
              <p className="text-muted-foreground">
                EZI Data Consultancy's liability is limited to the fees paid for the specific services. 
                We are not liable for indirect, incidental, or consequential damages.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-serif font-bold text-foreground mb-4">6. Contact Information</h2>
              <div className="p-4 bg-muted/20 rounded-lg">
                <p className="text-foreground font-medium">Linda Meals</p>
                <p className="text-muted-foreground">Email: <EMAIL></p>
                <p className="text-muted-foreground">Phone: +212648299882</p>
              </div>
            </section>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
