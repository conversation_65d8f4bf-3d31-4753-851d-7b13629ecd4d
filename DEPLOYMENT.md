# EZI Data Consultancy - Deployment Guide

This guide provides step-by-step instructions for deploying your EZI Data Consultancy website to various platforms.

## 🚀 Quick Deploy to Vercel (Recommended)

Vercel is the recommended platform for Next.js applications and offers the best performance and ease of use.

### Method 1: Deploy from v0 (Easiest)

1. **Click Deploy Button**: In the v0 interface, click the "Deploy" button in the top-right corner
2. **Connect GitHub**: Link your GitHub account if not already connected
3. **Create Repository**: v0 will create a new GitHub repository with your code
4. **Auto-Deploy**: Vercel will automatically deploy your site
5. **Get URL**: Your site will be live at `https://your-project-name.vercel.app`

### Method 2: Manual Vercel Deployment

1. **Download Code**: Click "Download Code" in v0 and extract the files
2. **Create GitHub Repository**:
   \`\`\`bash
   git init
   git add .
   git commit -m "Initial commit"
   git remote add origin https://github.com/yourusername/ezi-data-consultancy.git
   git push -u origin main
   \`\`\`
3. **Connect to Vercel**:
   - Go to [vercel.com](https://vercel.com)
   - Click "New Project"
   - Import your GitHub repository
   - Click "Deploy"

## 🌐 Alternative Deployment Options

### Netlify

1. **Build Settings**:
   - Build command: `npm run build`
   - Publish directory: `out`
   - Node version: 18.x

2. **Deploy Steps**:
   - Connect your GitHub repository to Netlify
   - Configure build settings
   - Deploy automatically on git push

### Traditional Web Hosting

1. **Build for Static Export**:
   \`\`\`bash
   npm run build
   npm run export
   \`\`\`
2. **Upload Files**: Upload the `out` folder contents to your web server
3. **Configure Server**: Ensure your server supports HTML5 routing

## ⚙️ Environment Configuration

### Required Environment Variables

Currently, no environment variables are required for basic functionality.

### Optional Environment Variables

For future enhancements, you may want to add:

\`\`\`env
# Analytics (Google Analytics)
NEXT_PUBLIC_GA_ID=your-ga-id

# Contact Form (if switching to external service)
NEXT_PUBLIC_FORMSPREE_URL=your-formspree-url

# Custom Domain
NEXT_PUBLIC_SITE_URL=https://your-domain.com
\`\`\`

## 🔧 Post-Deployment Configuration

### 1. Custom Domain Setup (Vercel)

1. **Add Domain**:
   - Go to your Vercel project dashboard
   - Click "Settings" → "Domains"
   - Add your custom domain

2. **DNS Configuration**:
   - Add CNAME record: `www` → `your-project.vercel.app`
   - Add A record: `@` → `76.76.19.61`

3. **SSL Certificate**: Automatically provisioned by Vercel

### 2. SEO Configuration

1. **Google Search Console**:
   - Add your domain to Google Search Console
   - Submit your sitemap: `https://yourdomain.com/sitemap.xml`

2. **Google Analytics** (Optional):
   - Create GA4 property
   - Add tracking ID to environment variables
   - Redeploy the site

### 3. Performance Monitoring

1. **Vercel Analytics**: Enable in project settings for performance insights
2. **Core Web Vitals**: Monitor through Google Search Console
3. **Uptime Monitoring**: Consider services like UptimeRobot

## 📊 Managing Your Website

### Content Updates

1. **Text Changes**:
   - Edit content in `app/page.tsx`
   - Commit and push changes
   - Site auto-deploys

2. **Image Updates**:
   - Replace images in `public/` directory
   - Maintain same filenames for automatic updates
   - Optimize images before uploading

3. **Style Changes**:
   - Modify colors in `app/globals.css`
   - Update component styles in `app/page.tsx`

### Contact Form Management

The current contact form uses `mailto:` links. To upgrade to a form service:

1. **Choose a Service**: Formspree, Netlify Forms, or EmailJS
2. **Update Form Action**: Modify the form in `app/page.tsx`
3. **Add Environment Variables**: Configure service credentials
4. **Redeploy**: Push changes to trigger deployment

### Analytics and Monitoring

1. **Traffic Analysis**:
   - Use Vercel Analytics (built-in)
   - Google Analytics for detailed insights
   - Monitor Core Web Vitals

2. **Performance Monitoring**:
   - Vercel Speed Insights
   - Lighthouse audits
   - GTmetrix for detailed analysis

## 🔒 Security Best Practices

### 1. HTTPS Configuration
- Vercel provides automatic HTTPS
- Ensure all external resources use HTTPS
- Configure security headers

### 2. Content Security Policy
Add to `next.config.js`:
\`\`\`javascript
const securityHeaders = [
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  }
]
\`\`\`

### 3. Regular Updates
- Keep dependencies updated
- Monitor security advisories
- Regular backups of content

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**:
   - Check Node.js version (18.x recommended)
   - Clear cache: `npm run clean`
   - Reinstall dependencies: `rm -rf node_modules && npm install`

2. **Images Not Loading**:
   - Verify images are in `public/` directory
   - Check file paths and extensions
   - Ensure proper image optimization

3. **Styling Issues**:
   - Clear browser cache
   - Check Tailwind CSS compilation
   - Verify CSS custom properties

### Performance Optimization

1. **Image Optimization**:
   - Use WebP format when possible
   - Implement lazy loading
   - Optimize image sizes

2. **Code Splitting**:
   - Already implemented with Next.js
   - Monitor bundle sizes
   - Use dynamic imports for large components

## 📞 Support and Maintenance

### Regular Maintenance Tasks

1. **Monthly**:
   - Update dependencies
   - Check performance metrics
   - Review analytics data

2. **Quarterly**:
   - Security audit
   - Content review and updates
   - SEO performance analysis

3. **Annually**:
   - Design refresh consideration
   - Technology stack review
   - Comprehensive security audit

### Getting Help

1. **Technical Issues**: Check Next.js and Vercel documentation
2. **Design Updates**: Refer to the design system in `globals.css`
3. **Performance Issues**: Use Vercel Analytics and Lighthouse

---

## 🎯 Success Checklist

After deployment, verify:

- [ ] Site loads correctly on desktop, tablet, and mobile
- [ ] All images display properly
- [ ] Contact form functions correctly
- [ ] SEO meta tags are present
- [ ] Site is accessible via custom domain (if configured)
- [ ] HTTPS is working
- [ ] Google Search Console is configured
- [ ] Analytics are tracking (if configured)

Your EZI Data Consultancy website is now live and ready to impress enterprise clients!

---

**Need Help?** Contact your development team or refer to the platform-specific documentation for advanced configurations.
