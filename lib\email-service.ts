'use client'

interface EmailData {
  name: string
  email: string
  company?: string
  message: string
}

// EmailJS integration (client-side)
export async function sendEmailWithEmailJS(data: EmailData): Promise<boolean> {
  const serviceId = process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID
  const templateId = process.env.NEXT_PUBLIC_EMAILJS_TEMPLATE_ID
  const publicKey = process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY

  if (!serviceId || !templateId || !publicKey) {
    console.log('EmailJS not configured. Form data:', data)
    return false
  }

  try {
    // Dynamically import EmailJS to avoid SSR issues
    const emailjs = await import('@emailjs/browser')
    
    const templateParams = {
      from_name: data.name,
      from_email: data.email,
      company: data.company || 'Not specified',
      message: data.message,
      to_email: process.env.NEXT_PUBLIC_COMPANY_EMAIL,
    }

    const response = await emailjs.send(
      serviceId,
      templateId,
      templateParams,
      publicKey
    )

    return response.status === 200
  } catch (error) {
    console.error('EmailJS error:', error)
    return false
  }
}

// Fallback: Simple mailto link (always works)
export function sendEmailWithMailto(data: EmailData): boolean {
  const subject = `Contact Form Submission from ${data.name}`
  const body = `
Name: ${data.name}
Email: ${data.email}
Company: ${data.company || 'Not specified'}

Message:
${data.message}

---
Sent from ${process.env.NEXT_PUBLIC_SITE_URL || 'website'} contact form
  `.trim()

  const mailtoLink = `mailto:${process.env.NEXT_PUBLIC_COMPANY_EMAIL}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
  
  // Open user's email client
  window.open(mailtoLink, '_blank')
  return true
}

// Main email function with fallback
export async function sendContactEmail(data: EmailData): Promise<{ success: boolean; method: string }> {
  // Try EmailJS first (if configured)
  const emailJSSuccess = await sendEmailWithEmailJS(data)
  if (emailJSSuccess) {
    return { success: true, method: 'EmailJS' }
  }

  // Fallback to mailto (always works)
  const mailtoSuccess = sendEmailWithMailto(data)
  return { success: mailtoSuccess, method: 'mailto' }
}
