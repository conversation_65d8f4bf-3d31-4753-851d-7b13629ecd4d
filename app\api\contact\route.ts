import { NextRequest, NextResponse } from 'next/server'
import { rateLimit } from '@/lib/rate-limit'

interface ContactFormData {
  name: string
  email: string
  company?: string
  message: string
}

function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, '')
}

function validateFormData(data: ContactFormData): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!data.name || data.name.length < 2) {
    errors.push('Name must be at least 2 characters long')
  }

  if (!data.email || !validateEmail(data.email)) {
    errors.push('Please provide a valid email address')
  }

  if (!data.message || data.message.length < 10) {
    errors.push('Message must be at least 10 characters long')
  }

  if (data.name && data.name.length > 100) {
    errors.push('Name must be less than 100 characters')
  }

  if (data.company && data.company.length > 100) {
    errors.push('Company name must be less than 100 characters')
  }

  if (data.message && data.message.length > 1000) {
    errors.push('Message must be less than 1000 characters')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const rateLimitResult = rateLimit(
      request,
      parseInt(process.env.FORM_RATE_LIMIT_REQUESTS || '5'),
      parseInt(process.env.FORM_RATE_LIMIT_WINDOW || '900000')
    )

    if (!rateLimitResult.success) {
      return NextResponse.json(
        { 
          error: 'Too many requests. Please try again later.',
          resetTime: rateLimitResult.resetTime
        },
        { status: 429 }
      )
    }

    const body = await request.json()
    
    // Sanitize inputs
    const formData: ContactFormData = {
      name: sanitizeInput(body.name || ''),
      email: sanitizeInput(body.email || ''),
      company: body.company ? sanitizeInput(body.company) : undefined,
      message: sanitizeInput(body.message || '')
    }

    // Validate form data
    const validation = validateFormData(formData)
    if (!validation.isValid) {
      return NextResponse.json(
        { error: 'Validation failed', details: validation.errors },
        { status: 400 }
      )
    }

    // TODO: Implement actual email sending
    // For now, we'll just log the form submission
    console.log('Contact form submission:', {
      name: formData.name,
      email: formData.email,
      company: formData.company,
      message: formData.message,
      timestamp: new Date().toISOString(),
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
    })

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    return NextResponse.json(
      { 
        success: true, 
        message: 'Thank you for your message. We will contact you within 24 hours.',
        remaining: rateLimitResult.remaining
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('Contact form error:', error)
    return NextResponse.json(
      { error: 'Internal server error. Please try again later.' },
      { status: 500 }
    )
  }
}
