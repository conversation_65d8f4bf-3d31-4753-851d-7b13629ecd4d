import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON><PERSON><PERSON>, <PERSON> } from "next/font/google"
import { GoogleAnalytics, GoogleTagManager } from "@/components/analytics"
import "./globals.css"

const montserrat = Montserrat({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-montserrat",
  weight: ["400", "600", "700", "900"],
})

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
  weight: ["400", "500", "600"],
})

export const metadata: Metadata = {
  title: "EZI Data Consultancy - Enterprise Fraud & Revenue Solutions",
  description:
    "Premium telecom fraud management, revenue assurance, and BSS/OSS optimization. Trusted by enterprise clients worldwide. Stop fraud, secure revenue, optimize systems.",
  keywords:
    "telecom fraud management, revenue assurance, BSS OSS optimization, telecom consultancy, fraud detection, billing reconciliation, telecom security, revenue recovery",
  authors: [{ name: "EZI Data Consultancy" }],
  creator: "EZI Data Consultancy",
  publisher: "EZI Data Consultancy",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://ezidataconsultancy.com",
    title: "EZI Data Consultancy - Enterprise Fraud & Revenue Solutions",
    description:
      "Premium telecom fraud management, revenue assurance, and BSS/OSS optimization. Stop fraud, secure revenue, optimize systems. Trusted by enterprise clients worldwide.",
    siteName: "EZI Data Consultancy",
    images: [
      {
        url: "/og-image.png",
        width: 1200,
        height: 630,
        alt: "EZI Data Consultancy - Enterprise Fraud & Revenue Solutions",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "EZI Data Consultancy - Enterprise Fraud & Revenue Solutions",
    description:
      "Premium telecom fraud management, revenue assurance, and BSS/OSS optimization. Stop fraud, secure revenue, optimize systems.",
    images: ["/og-image.png"],
  },
  generator: "v0.app",
  other: {
    "application/ld+json": JSON.stringify({
      "@context": "https://schema.org",
      "@type": "Organization",
      name: "EZI Data Consultancy",
      description: "Premium telecom fraud management, revenue assurance, and BSS/OSS optimization consultancy",
      url: "https://ezidataconsultancy.com",
      logo: "https://ezidataconsultancy.com/logo.png",
      contactPoint: {
        "@type": "ContactPoint",
        telephone: "******-123-4567",
        contactType: "customer service",
        email: "<EMAIL>",
      },
      sameAs: [],
      address: {
        "@type": "PostalAddress",
        addressCountry: "US",
      },
      service: [
        {
          "@type": "Service",
          name: "Fraud Management",
          description: "Telecom fraud detection and prevention solutions",
        },
        {
          "@type": "Service",
          name: "Revenue Assurance",
          description: "Revenue leak detection and billing optimization",
        },
        {
          "@type": "Service",
          name: "BSS/OSS Tuning",
          description: "Business and operational support system optimization",
        },
      ],
    }),
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={`${montserrat.variable} ${inter.variable} antialiased`}>
      <head>
        <link rel="icon" href="/favicon.ico" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      </head>
      <body className="font-sans">
        <GoogleAnalytics />
        <GoogleTagManager />
        {children}
      </body>
    </html>
  )
}
